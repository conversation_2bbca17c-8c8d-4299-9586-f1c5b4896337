package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.HotelOrder;
import org.apache.commons.lang3.tuple.Pair;

/**
 * 酒店订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface IHotelOrderService 
{
    /**
     * 查询酒店订单
     * 
     * @param orderId 酒店订单主键
     * @return 酒店订单
     */
    public HotelOrder selectHotelOrderByOrderId(Long orderId);

    /**
     * 根据订单号查询酒店订单
     * 
     * @param orderNo 订单号
     * @return 酒店订单
     */
    public HotelOrder selectHotelOrderByOrderNo(String orderNo);

    /**
     * 根据微信交易号查询酒店订单
     * 
     * @param transactionId 微信交易号
     * @return 酒店订单
     */
    public HotelOrder selectHotelOrderByTransactionId(String transactionId);

    /**
     * 查询酒店订单列表
     * 
     * @param hotelOrder 酒店订单
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectHotelOrderList(HotelOrder hotelOrder);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectHotelOrderListByUserId(Long userId);

    /**
     * 根据openid查询订单列表
     * 
     * @param openid 微信openid
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectHotelOrderListByOpenid(String openid);

    /**
     * 根据会议ID查询订单列表
     * 
     * @param conferenceId 会议ID
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectHotelOrderListByConferenceId(Long conferenceId);

    /**
     * 查询待支付订单列表（超时订单）
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 酒店订单集合
     */
    public List<HotelOrder> selectTimeoutPendingOrders(int timeoutMinutes);

    /**
     * 新增酒店订单
     * 
     * @param hotelOrder 酒店订单
     * @return 结果
     */
    public int insertHotelOrder(HotelOrder hotelOrder);

    /**
     * 修改酒店订单
     * 
     * @param hotelOrder 酒店订单
     * @return 结果
     */
    public int updateHotelOrder(HotelOrder hotelOrder);

    /**
     * 更新订单支付信息
     * 
     * @param orderNo 订单号
     * @param transactionId 微信交易号
     * @param paymentTime 支付时间
     * @param orderStatus 订单状态
     * @param paymentStatus 支付状态
     * @param paymentMethod 支付方式
     * @return 结果
     */
    public int updateOrderPaymentInfo(String orderNo, String transactionId, java.util.Date paymentTime,
                                      String orderStatus, String paymentStatus, String paymentMethod);

    /**
     * 更新订单状态
     * 
     * @param orderNo 订单号
     * @param orderStatus 订单状态
     * @param updateBy 更新人
     * @param changeReason 变更原因
     * @return 结果
     */
    public int updateOrderStatus(String orderNo, String orderStatus, String updateBy, String changeReason);

    /**
     * 更新支付状态
     * 
     * @param orderNo 订单号
     * @param paymentStatus 支付状态
     * @param updateBy 更新人
     * @param changeReason 变更原因
     * @return 结果
     */
    public int updatePaymentStatus(String orderNo, String paymentStatus, String updateBy, String changeReason);

    /**
     * 取消订单
     * 
     * @param orderNo 订单号
     * @param cancelReason 取消原因
     * @param updateBy 更新人
     * @return 结果
     */
    public int cancelOrder(String orderNo, String cancelReason, String updateBy);

    /**
     * 确认订单
     *
     * @param orderNo 订单号
     * @param updateBy 更新人
     * @return 结果
     */
    public int confirmOrder(String orderNo, String updateBy);

    /**
     * 退款订单
     *
     * @param orderNo 订单号
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @param updateBy 更新人
     * @return 结果
     */
    public Pair<Integer, String> refundOrder(String orderNo, java.math.BigDecimal refundAmount, String refundReason, String updateBy);

    /**
     * 刷新订单状态
     * 从微信支付查询最新状态并更新到数据库
     *
     * @param orderNo 订单号
     * @param updateBy 更新人
     * @return 结果
     */
    public int refreshOrderStatus(String orderNo, String updateBy);

    /**
     * 批量删除酒店订单
     * 
     * @param orderIds 需要删除的酒店订单主键集合
     * @return 结果
     */
    public int deleteHotelOrderByOrderIds(Long[] orderIds);

    /**
     * 删除酒店订单信息
     * 
     * @param orderId 酒店订单主键
     * @return 结果
     */
    public int deleteHotelOrderByOrderId(Long orderId);

    /**
     * 统计订单数量
     * 
     * @param hotelOrder 查询条件
     * @return 订单数量
     */
    public int countHotelOrders(HotelOrder hotelOrder);

    /**
     * 统计用户订单数量
     * 
     * @param userId 用户ID
     * @param orderStatus 订单状态
     * @return 订单数量
     */
    public int countUserOrders(Long userId, String orderStatus);

    /**
     * 生成订单号
     * 
     * @param prefix 前缀
     * @return 订单号
     */
    public String generateOrderNo(String prefix);

    /**
     * 处理超时订单
     *
     * @param timeoutMinutes 超时分钟数
     * @return 处理的订单数量
     */
    public int handleTimeoutOrders(int timeoutMinutes);

    /**
     * 统计指定房间在指定日期范围内的已预订数量
     *
     * @param roomId 房间ID
     * @param conferenceId 会议ID
     * @param checkinDate 入住日期
     * @param checkoutDate 退房日期
     * @return 已预订数量（包括待支付、已支付、已确认的订单，不包括已取消和已退款的订单）
     */
    public int countBookedRooms(Long roomId, Long conferenceId, java.util.Date checkinDate, java.util.Date checkoutDate);

    /**
     * 带库存校验和并发控制的创建订单方法
     *
     * @param hotelOrder 酒店订单
     * @param totalInventory 房间总库存
     * @param checkinDate 入住日期
     * @param checkoutDate 退房日期
     * @return 创建结果，包含库存校验信息
     */
    public OrderCreationResult createOrderWithInventoryCheck(HotelOrder hotelOrder, int totalInventory,
                                                           java.util.Date checkinDate, java.util.Date checkoutDate);

    /**
     * 订单创建结果类
     */
    public static class OrderCreationResult {
        private boolean success;
        private String message;
        private int availableCount;
        private HotelOrder hotelOrder;

        public OrderCreationResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public OrderCreationResult(boolean success, String message, int availableCount, HotelOrder hotelOrder) {
            this.success = success;
            this.message = message;
            this.availableCount = availableCount;
            this.hotelOrder = hotelOrder;
        }

        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public int getAvailableCount() { return availableCount; }
        public void setAvailableCount(int availableCount) { this.availableCount = availableCount; }
        public HotelOrder getHotelOrder() { return hotelOrder; }
        public void setHotelOrder(HotelOrder hotelOrder) { this.hotelOrder = hotelOrder; }
    }
}
