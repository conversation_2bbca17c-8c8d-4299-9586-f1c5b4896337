package com.ruoyi.system.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.HotelOrderMapper;
import com.ruoyi.system.mapper.HotelOrderStatusLogMapper;
import com.ruoyi.system.domain.HotelOrder;
import com.ruoyi.system.domain.HotelOrderStatusLog;
import com.ruoyi.system.service.IHotelOrderService;
import com.ruoyi.system.service.IWechatPayService;
import com.ruoyi.common.core.domain.WechatPayV3Result;
import com.ruoyi.common.core.domain.WechatRefundResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 酒店订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class HotelOrderServiceImpl implements IHotelOrderService {
    private static final Logger log = LoggerFactory.getLogger(HotelOrderServiceImpl.class);

    @Autowired
    private HotelOrderMapper hotelOrderMapper;

    @Autowired
    private HotelOrderStatusLogMapper hotelOrderStatusLogMapper;

    @Autowired
    private IWechatPayService wechatPayService;

    @Autowired
    private RoomLockManager roomLockManager;

    /**
     * 查询酒店订单
     *
     * @param orderId 酒店订单主键
     * @return 酒店订单
     */
    @Override
    public HotelOrder selectHotelOrderByOrderId(Long orderId) {
        return hotelOrderMapper.selectHotelOrderByOrderId(orderId);
    }

    /**
     * 根据订单号查询酒店订单
     *
     * @param orderNo 订单号
     * @return 酒店订单
     */
    @Override
    public HotelOrder selectHotelOrderByOrderNo(String orderNo) {
        return hotelOrderMapper.selectHotelOrderByOrderNo(orderNo);
    }

    /**
     * 根据微信交易号查询酒店订单
     *
     * @param transactionId 微信交易号
     * @return 酒店订单
     */
    @Override
    public HotelOrder selectHotelOrderByTransactionId(String transactionId) {
        return hotelOrderMapper.selectHotelOrderByTransactionId(transactionId);
    }

    /**
     * 查询酒店订单列表
     *
     * @param hotelOrder 酒店订单
     * @return 酒店订单
     */
    @Override
    public List<HotelOrder> selectHotelOrderList(HotelOrder hotelOrder) {
        return hotelOrderMapper.selectHotelOrderList(hotelOrder);
    }

    /**
     * 根据用户ID查询订单列表
     *
     * @param userId 用户ID
     * @return 酒店订单集合
     */
    @Override
    public List<HotelOrder> selectHotelOrderListByUserId(Long userId) {
        return hotelOrderMapper.selectHotelOrderListByUserId(userId);
    }

    /**
     * 根据openid查询订单列表
     *
     * @param openid 微信openid
     * @return 酒店订单集合
     */
    @Override
    public List<HotelOrder> selectHotelOrderListByOpenid(String openid) {
        return hotelOrderMapper.selectHotelOrderListByOpenid(openid);
    }

    /**
     * 根据会议ID查询订单列表
     *
     * @param conferenceId 会议ID
     * @return 酒店订单集合
     */
    @Override
    public List<HotelOrder> selectHotelOrderListByConferenceId(Long conferenceId) {
        return hotelOrderMapper.selectHotelOrderListByConferenceId(conferenceId);
    }

    /**
     * 查询待支付订单列表（超时订单）
     *
     * @param timeoutMinutes 超时分钟数
     * @return 酒店订单集合
     */
    @Override
    public List<HotelOrder> selectTimeoutPendingOrders(int timeoutMinutes) {
        return hotelOrderMapper.selectTimeoutPendingOrders(timeoutMinutes);
    }

    /**
     * 新增酒店订单
     *
     * @param hotelOrder 酒店订单
     * @return 结果
     */
    @Override
    @Transactional
    public int insertHotelOrder(HotelOrder hotelOrder) {
        // 生成订单号
        if (StringUtils.isEmpty(hotelOrder.getOrderNo())) {
            hotelOrder.setOrderNo(generateOrderNo("HT"));
        }

        // 设置默认状态
        if (StringUtils.isEmpty(hotelOrder.getOrderStatus())) {
            hotelOrder.setOrderStatus(HotelOrder.OrderStatus.PENDING);
        }
        if (StringUtils.isEmpty(hotelOrder.getPaymentStatus())) {
            hotelOrder.setPaymentStatus(HotelOrder.PaymentStatus.UNPAID);
        }

        int result = hotelOrderMapper.insertHotelOrder(hotelOrder);

        // 记录状态变更日志
        if (result > 0) {
            logStatusChange(hotelOrder.getOrderId(), hotelOrder.getOrderNo(), null,
                    hotelOrder.getOrderStatus(), HotelOrderStatusLog.StatusType.ORDER,
                    "创建订单", hotelOrder.getCreateBy());
        }

        return result;
    }

    /**
     * 修改酒店订单
     *
     * @param hotelOrder 酒店订单
     * @return 结果
     */
    @Override
    public int updateHotelOrder(HotelOrder hotelOrder) {
        return hotelOrderMapper.updateHotelOrder(hotelOrder);
    }

    /**
     * 更新订单支付信息
     *
     * @param orderNo       订单号
     * @param transactionId 微信交易号
     * @param paymentTime   支付时间
     * @param orderStatus   订单状态
     * @param paymentStatus 支付状态
     * @param paymentMethod 支付方式
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOrderPaymentInfo(String orderNo, String transactionId, Date paymentTime,
                                      String orderStatus, String paymentStatus, String paymentMethod) {
        // 查询原订单信息
        HotelOrder originalOrder = hotelOrderMapper.selectHotelOrderByOrderNo(orderNo);
        if (originalOrder == null) {
            log.error("订单不存在，订单号: {}", orderNo);
            return 0;
        }

        int result = hotelOrderMapper.updateOrderPaymentInfo(orderNo, transactionId, paymentTime,
                orderStatus, paymentStatus, paymentMethod);

        // 记录状态变更日志
        if (result > 0) {
            if (!originalOrder.getOrderStatus().equals(orderStatus)) {
                logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getOrderStatus(),
                        orderStatus, HotelOrderStatusLog.StatusType.ORDER, "支付成功", "system");
            }
            if (!originalOrder.getPaymentStatus().equals(paymentStatus)) {
                logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getPaymentStatus(),
                        paymentStatus, HotelOrderStatusLog.StatusType.PAYMENT, "支付成功", "system");
            }
        }

        return result;
    }

    /**
     * 更新订单状态
     *
     * @param orderNo      订单号
     * @param orderStatus  订单状态
     * @param updateBy     更新人
     * @param changeReason 变更原因
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOrderStatus(String orderNo, String orderStatus, String updateBy, String changeReason) {
        // 查询原订单信息
        HotelOrder originalOrder = hotelOrderMapper.selectHotelOrderByOrderNo(orderNo);
        if (originalOrder == null) {
            log.error("订单不存在，订单号: {}", orderNo);
            return 0;
        }

        int result = hotelOrderMapper.updateOrderStatus(orderNo, orderStatus, updateBy);

        // 记录状态变更日志
        if (result > 0 && !originalOrder.getOrderStatus().equals(orderStatus)) {
            logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getOrderStatus(),
                    orderStatus, HotelOrderStatusLog.StatusType.ORDER, changeReason, updateBy);
        }

        return result;
    }

    /**
     * 更新支付状态
     *
     * @param orderNo       订单号
     * @param paymentStatus 支付状态
     * @param updateBy      更新人
     * @param changeReason  变更原因
     * @return 结果
     */
    @Override
    @Transactional
    public int updatePaymentStatus(String orderNo, String paymentStatus, String updateBy, String changeReason) {
        // 查询原订单信息
        HotelOrder originalOrder = hotelOrderMapper.selectHotelOrderByOrderNo(orderNo);
        if (originalOrder == null) {
            log.error("订单不存在，订单号: {}", orderNo);
            return 0;
        }

        int result = hotelOrderMapper.updatePaymentStatus(orderNo, paymentStatus, updateBy);

        // 记录状态变更日志
        if (result > 0 && !originalOrder.getPaymentStatus().equals(paymentStatus)) {
            logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getPaymentStatus(),
                    paymentStatus, HotelOrderStatusLog.StatusType.PAYMENT, changeReason, updateBy);
        }

        return result;
    }

    /**
     * 取消订单
     *
     * @param orderNo      订单号
     * @param cancelReason 取消原因
     * @param updateBy     更新人
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelOrder(String orderNo, String cancelReason, String updateBy) {
        // 查询原订单信息
        HotelOrder originalOrder = hotelOrderMapper.selectHotelOrderByOrderNo(orderNo);
        if (originalOrder == null) {
            log.error("订单不存在，订单号: {}", orderNo);
            return 0;
        }

        int result = hotelOrderMapper.cancelOrder(orderNo, cancelReason, updateBy);

        // 记录状态变更日志
        if (result > 0) {
            logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getOrderStatus(),
                    HotelOrder.OrderStatus.CANCELLED, HotelOrderStatusLog.StatusType.ORDER,
                    cancelReason, updateBy);
        }

        return result;
    }

    /**
     * 确认订单
     *
     * @param orderNo  订单号
     * @param updateBy 更新人
     * @return 结果
     */
    @Override
    @Transactional
    public int confirmOrder(String orderNo, String updateBy) {
        return updateOrderStatus(orderNo, HotelOrder.OrderStatus.CONFIRMED, updateBy, "确认订单");
    }

    /**
     * 退款订单
     *
     * @param orderNo      订单号
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @param updateBy     更新人
     * @return 结果
     */
    @Override
    @Transactional
    public Pair<Integer, String> refundOrder(String orderNo, BigDecimal refundAmount, String refundReason, String updateBy) {
        // 查询原订单信息
        HotelOrder originalOrder = hotelOrderMapper.selectHotelOrderByOrderNo(orderNo);
        if (originalOrder == null) {
            log.error("订单不存在，订单号: {}", orderNo);
            return Pair.of(0, "订单不存在");
        }

        // 验证订单状态，只有已支付或已确认的订单才能退款
        if (!HotelOrder.OrderStatus.PAID.equals(originalOrder.getOrderStatus()) &&
                !HotelOrder.OrderStatus.CONFIRMED.equals(originalOrder.getOrderStatus())) {
            log.error("订单状态不允许退款，订单号: {}, 当前状态: {}", orderNo, originalOrder.getOrderStatus());
            return Pair.of(0, "订单状态不允许退款");
        }

        // 验证退款金额
        if (refundAmount == null || refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("退款金额无效，订单号: {}, 退款金额: {}", orderNo, refundAmount);
            return Pair.of(0, "退款金额无效");
        }

        if (refundAmount.compareTo(originalOrder.getTotalAmount()) > 0) {
            log.error("退款金额不能超过订单总金额，订单号: {}, 退款金额: {}, 订单总金额: {}",
                    orderNo, refundAmount, originalOrder.getTotalAmount());
            return Pair.of(0, "退款金额不能超过订单总金额");
        }

        // 验证是否有微信支付交易号
        if (StringUtils.isEmpty(originalOrder.getTransactionId())) {
            log.error("订单没有微信支付交易号，无法退款，订单号: {}", orderNo);
            return Pair.of(0, "订单没有微信支付交易号");
        }

        // 生成退款单号
        String outRefundNo = "RF" + orderNo + "_" + System.currentTimeMillis();

        // 调用微信支付退款接口
        try {
            // 将金额转换为分
            Long refundAmountFen = refundAmount.multiply(new BigDecimal(100)).longValue();
            Long totalAmountFen = originalOrder.getTotalAmount().multiply(new BigDecimal(100)).longValue();

            log.info("开始调用微信支付退款，订单号: {}, 退款单号: {}, 退款金额: {}分",
                    orderNo, outRefundNo, refundAmountFen);

            WechatRefundResult refundResult = wechatPayService.refundOrder(
                    orderNo, outRefundNo, refundAmountFen, totalAmountFen, refundReason);

            if (refundResult == null) {
                log.error("微信支付退款失败，订单号: {}, 退款状态: {}", orderNo, "返回结果为空");
                return Pair.of(0, "微信支付退款失败");
            }

            if ((!refundResult.isSuccess() && !refundResult.isProcessing())) {
                log.error("微信支付退款失败，订单号: {}, 退款状态: {}",
                        orderNo, refundResult.getStatus());
                return Pair.of(0, refundResult.getMessage());
            }

            log.info("微信支付退款成功，订单号: {}, 退款单号: {}", orderNo, outRefundNo);

        } catch (Exception e) {
            log.error("调用微信支付退款接口异常，订单号: " + orderNo, e);
            return Pair.of(0, "调用微信支付退款接口异常");
        }

        // 更新数据库订单状态
        int result = hotelOrderMapper.refundOrder(orderNo, refundAmount, refundReason, updateBy);

        // 记录状态变更日志
        if (result > 0) {
            logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getOrderStatus(),
                    HotelOrder.OrderStatus.REFUNDED, HotelOrderStatusLog.StatusType.ORDER,
                    refundReason, updateBy);

            logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getPaymentStatus(),
                    HotelOrder.PaymentStatus.REFUNDED, HotelOrderStatusLog.StatusType.PAYMENT,
                    refundReason, updateBy);

            log.info("订单退款成功，订单号: {}, 退款金额: {}, 退款单号: {}", orderNo, refundAmount, outRefundNo);
        }

        return Pair.of(result, "");
    }

    /**
     * 刷新订单状态
     * 从微信支付查询最新状态并更新到数据库
     *
     * @param orderNo  订单号
     * @param updateBy 更新人
     * @return 结果
     */
    @Override
    @Transactional
    public int refreshOrderStatus(String orderNo, String updateBy) {
        // 查询原订单信息
        HotelOrder originalOrder = hotelOrderMapper.selectHotelOrderByOrderNo(orderNo);
        if (originalOrder == null) {
            log.error("订单不存在，订单号: {}", orderNo);
            return 0;
        }

        try {
            WechatPayV3Result payResult = null;

            // 1. 如果有微信交易号，优先使用微信交易号查询
            if (StringUtils.isNotEmpty(originalOrder.getTransactionId())) {
                log.info("使用微信交易号查询订单状态，订单号: {}, 微信交易号: {}",
                        orderNo, originalOrder.getTransactionId());
                payResult = wechatPayService.queryPayOrderByTransactionId(originalOrder.getTransactionId());
            }
            // 2. 如果没有微信交易号，使用商户订单号查询
            else {
                log.info("使用商户订单号查询订单状态，订单号: {}", orderNo);
                payResult = wechatPayService.queryPayOrder(orderNo);
            }

            if (payResult == null) {
                log.error("查询微信支付订单失败，订单号: {}", orderNo);
                return 0;
            }

            // 3. 如果原来没有微信交易号，但查询结果有，则更新到数据库
            if (StringUtils.isEmpty(originalOrder.getTransactionId()) &&
                    StringUtils.isNotEmpty(payResult.getTransactionId())) {
                log.info("更新微信交易号，订单号: {}, 微信交易号: {}", orderNo, payResult.getTransactionId());
                hotelOrderMapper.updateTransactionId(orderNo, payResult.getTransactionId(), updateBy);
            }

            // 4. 根据微信支付状态更新本地订单状态
            return updateOrderStatusFromWechatPay(originalOrder, payResult, updateBy);

        } catch (Exception e) {
            log.error("刷新订单状态异常，订单号: " + orderNo, e);
            return 0;
        }
    }

    /**
     * 根据微信支付状态更新本地订单状态
     */
    private int updateOrderStatusFromWechatPay(HotelOrder originalOrder, WechatPayV3Result payResult, String updateBy) {
        String orderNo = originalOrder.getOrderNo();
        String wechatTradeState = payResult.getTradeState();
        int updateCount = 0;

        log.info("微信支付状态: {}, 本地订单状态: {}, 订单号: {}",
                wechatTradeState, originalOrder.getOrderStatus(), orderNo);

        // 根据微信支付状态更新本地状态
        switch (wechatTradeState) {
            case "SUCCESS": // 支付成功
                if (HotelOrder.OrderStatus.PENDING.equals(originalOrder.getOrderStatus())) {
                    // 更新订单状态为已支付
                    updateCount += hotelOrderMapper.updateOrderStatus(orderNo,
                            HotelOrder.OrderStatus.PAID, updateBy);
                    updateCount += hotelOrderMapper.updatePaymentStatus(orderNo,
                            HotelOrder.PaymentStatus.PAID, updateBy);

                    // 记录状态变更日志
                    logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getOrderStatus(),
                            HotelOrder.OrderStatus.PAID, HotelOrderStatusLog.StatusType.ORDER,
                            "微信支付成功，自动更新状态", updateBy);

                    log.info("订单状态已更新为已支付，订单号: {}", orderNo);
                }
                break;

            case "NOTPAY": // 未支付
                // 保持待支付状态，无需更新
                log.info("订单未支付，保持当前状态，订单号: {}", orderNo);
                break;

            case "CLOSED": // 已关闭
                if (!HotelOrder.OrderStatus.CANCELLED.equals(originalOrder.getOrderStatus())) {
                    // 更新订单状态为已取消
                    updateCount += hotelOrderMapper.cancelOrder(orderNo, "微信支付订单已关闭", updateBy);

                    // 记录状态变更日志
                    logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getOrderStatus(),
                            HotelOrder.OrderStatus.CANCELLED, HotelOrderStatusLog.StatusType.ORDER,
                            "微信支付订单已关闭", updateBy);

                    log.info("订单状态已更新为已取消，订单号: {}", orderNo);
                }
                break;

            case "REVOKED": // 已撤销
                if (!HotelOrder.OrderStatus.CANCELLED.equals(originalOrder.getOrderStatus())) {
                    // 更新订单状态为已取消
                    updateCount += hotelOrderMapper.cancelOrder(orderNo, "微信支付订单已撤销", updateBy);

                    // 记录状态变更日志
                    logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getOrderStatus(),
                            HotelOrder.OrderStatus.CANCELLED, HotelOrderStatusLog.StatusType.ORDER,
                            "微信支付订单已撤销", updateBy);

                    log.info("订单状态已更新为已取消，订单号: {}", orderNo);
                }
                break;

            case "USERPAYING": // 用户支付中
                log.info("用户支付中，保持当前状态，订单号: {}", orderNo);
                break;

            case "REFUND": // 订单发生过退款
                updateCount += handleRefundStatus(originalOrder, updateBy);
                break;

            case "PAYERROR": // 支付失败
                log.info("支付失败，保持当前状态，订单号: {}", orderNo);
                break;

            default:
                log.warn("未知的微信支付状态: {}, 订单号: {}", wechatTradeState, orderNo);
                break;
        }

        return 1;
    }

    /**
     * 处理退款状态
     * 当微信支付返回REFUND状态时，查询退款详情并更新本地状态
     */
    private int handleRefundStatus(HotelOrder originalOrder, String updateBy) {
        String orderNo = originalOrder.getOrderNo();

        if (HotelOrder.OrderStatus.REFUNDED.equals(originalOrder.getOrderStatus())) {
            log.info("订单已是退款状态，无需更新，订单号: {}", orderNo);
            return 0;
        }

        log.info("检测到订单发生过退款，订单号: {}", orderNo);

        // 方案1: 直接更新为已退款状态（简单处理）
        // 因为微信支付返回REFUND状态，说明订单确实发生了退款
        try {
            int updateCount = 0;

            // 更新订单状态为已退款
            updateCount += hotelOrderMapper.updateOrderStatus(orderNo,
                    HotelOrder.OrderStatus.REFUNDED, updateBy);
            updateCount += hotelOrderMapper.updatePaymentStatus(orderNo,
                    HotelOrder.PaymentStatus.REFUNDED, updateBy);

            // 记录状态变更日志
            if (updateCount > 0) {
                logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getOrderStatus(),
                        HotelOrder.OrderStatus.REFUNDED, HotelOrderStatusLog.StatusType.ORDER,
                        "微信支付显示订单已退款，自动同步状态", updateBy);

                logStatusChange(originalOrder.getOrderId(), orderNo, originalOrder.getPaymentStatus(),
                        HotelOrder.PaymentStatus.REFUNDED, HotelOrderStatusLog.StatusType.PAYMENT,
                        "微信支付显示订单已退款，自动同步状态", updateBy);

                log.info("订单状态已同步为已退款，订单号: {}", orderNo);
            }

            return updateCount;

        } catch (Exception e) {
            log.error("处理退款状态异常，订单号: " + orderNo, e);
            return 0;
        }

        // 方案2: 查询具体退款详情（复杂处理，需要退款单号）
        // 如果需要更精确的退款信息，可以通过以下方式：
        // 1. 从数据库查询该订单的退款记录
        // 2. 调用微信支付退款查询接口
        // 3. 根据退款状态更新本地订单状态
        // 但这需要额外的退款单号信息，暂时使用简单处理方案
    }

    /**
     * 批量删除酒店订单
     *
     * @param orderIds 需要删除的酒店订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteHotelOrderByOrderIds(Long[] orderIds) {
        // 删除相关的状态变更记录
        for (Long orderId : orderIds) {
            hotelOrderStatusLogMapper.deleteHotelOrderStatusLogByOrderId(orderId);
        }
        return hotelOrderMapper.deleteHotelOrderByOrderIds(orderIds);
    }

    /**
     * 删除酒店订单信息
     *
     * @param orderId 酒店订单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteHotelOrderByOrderId(Long orderId) {
        // 删除相关的状态变更记录
        hotelOrderStatusLogMapper.deleteHotelOrderStatusLogByOrderId(orderId);
        return hotelOrderMapper.deleteHotelOrderByOrderId(orderId);
    }

    /**
     * 统计订单数量
     *
     * @param hotelOrder 查询条件
     * @return 订单数量
     */
    @Override
    public int countHotelOrders(HotelOrder hotelOrder) {
        return hotelOrderMapper.countHotelOrders(hotelOrder);
    }

    /**
     * 统计用户订单数量
     *
     * @param userId      用户ID
     * @param orderStatus 订单状态
     * @return 订单数量
     */
    @Override
    public int countUserOrders(Long userId, String orderStatus) {
        return hotelOrderMapper.countUserOrders(userId, orderStatus);
    }

    /**
     * 生成订单号
     *
     * @param prefix 前缀
     * @return 订单号
     */
    @Override
    public String generateOrderNo(String prefix) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        // 添加3位随机数
        int random = (int) (Math.random() * 1000);
        return prefix + timestamp + String.format("%03d", random);
    }

    /**
     * 处理超时订单
     *
     * @param timeoutMinutes 超时分钟数
     * @return 处理的订单数量
     */
    @Override
    @Transactional
    public int handleTimeoutOrders(int timeoutMinutes) {
        List<HotelOrder> timeoutOrders = hotelOrderMapper.selectTimeoutPendingOrders(timeoutMinutes);
        int count = 0;

        for (HotelOrder order : timeoutOrders) {
            int result = cancelOrder(order.getOrderNo(), "订单超时自动取消", "system");
            if (result > 0) {
                count++;
                log.info("订单超时自动取消，订单号: {}", order.getOrderNo());
            }
        }

        return count;
    }

    /**
     * 统计指定房间在指定日期范围内的已预订数量
     *
     * @param roomId       房间ID
     * @param conferenceId 会议ID
     * @param checkinDate  入住日期
     * @param checkoutDate 退房日期
     * @return 已预订数量（包括待支付、已支付、已确认的订单，不包括已取消和已退款的订单）
     */
    @Override
    public int countBookedRooms(Long roomId, Long conferenceId, java.util.Date checkinDate, java.util.Date checkoutDate) {
        return hotelOrderMapper.countBookedRooms(roomId, conferenceId, checkinDate, checkoutDate);
    }

    /**
     * 带库存校验和并发控制的创建订单方法
     *
     * @param hotelOrder     酒店订单
     * @param totalInventory 房间总库存
     * @param checkinDate    入住日期
     * @param checkoutDate   退房日期
     * @return 创建结果，包含库存校验信息
     */
    @Override
    @Transactional
    public OrderCreationResult createOrderWithInventoryCheck(HotelOrder hotelOrder, int totalInventory,
                                                             java.util.Date checkinDate, java.util.Date checkoutDate) {
        Long roomId = hotelOrder.getRoomId();
        Long conferenceId = hotelOrder.getConferenceId();

        try {
            // 使用分段锁，锁定特定房间，超时时间5秒
            return roomLockManager.executeWithLock(roomId, conferenceId, () -> {

                log.info("开始库存校验，房间ID: {}, 会议ID: {}, 总库存: {}", roomId, conferenceId, totalInventory);

                // 1. 检查总库存
                if (totalInventory <= 0) {
                    return new OrderCreationResult(false, "该房型暂无库存");
                }

                // 2. 统计已预订数量（在锁内重新查询，确保数据一致性）
                int bookedCount = countBookedRooms(roomId, conferenceId, checkinDate, checkoutDate);
                int availableCount = totalInventory - bookedCount;

                log.info("库存校验详情 - 房间ID: {}, 总库存: {}, 已预订: {}, 剩余: {}",
                        roomId, totalInventory, bookedCount, availableCount);

                // 3. 检查剩余库存
                if (availableCount <= 0) {
                    return new OrderCreationResult(false,
                            String.format("该房型在所选日期已满房，剩余数量: %d", availableCount),
                            availableCount, null);
                }

                // 4. 创建订单
                int orderResult = insertHotelOrder(hotelOrder);
                if (orderResult <= 0) {
                    return new OrderCreationResult(false, "创建订单失败");
                }

                log.info("订单创建成功，订单号: {}, 房间剩余数量: {}", hotelOrder.getOrderNo(), availableCount - 1);

                return new OrderCreationResult(true, "订单创建成功", availableCount - 1, hotelOrder);

            }, 5000); // 5秒超时

        } catch (Exception e) {
            log.error("创建订单时发生异常，房间ID: {}, 会议ID: {}", roomId, conferenceId, e);
            return new OrderCreationResult(false, "系统异常：" + e.getMessage());
        }
    }

    /**
     * 记录状态变更日志
     */
    private void logStatusChange(Long orderId, String orderNo, String oldStatus, String newStatus,
                                 String statusType, String changeReason, String operator) {
        try {
            hotelOrderStatusLogMapper.logStatusChange(orderId, orderNo, oldStatus, newStatus,
                    statusType, changeReason, operator);
        } catch (Exception e) {
            log.error("记录订单状态变更日志失败，订单号: {}", orderNo, e);
        }
    }
}
